# 🚀 SMS & Email Marketing Implementation Guide

> Hướng dẫn chi tiết triển khai các tính năng SMS và Email Marketing cho module Marketing

## 📋 Tổng quan Implementation

Dựa trên kế hoạch phát triển và cấu trúc hiện tại, tài liệu này cung cấp hướng dẫn từng bước để triển khai các tính năng SMS và Email Marketing.

## 🎯 Phase 1: Cơ sở dữ liệu và Entities (Tuần 1-2)

### 1.1. Tạo Entities mới cho Email Marketing

#### 1.1.1. UserEmailCampaignStats Entity
```typescript
// src/modules/marketing/user/entities/user-email-campaign-stats.entity.ts
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('user_email_campaign_stats')
export class UserEmailCampaignStats {
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  @Column({ name: 'user_id', nullable: false, comment: 'ID người dùng' })
  userId: number;

  @Column({ name: 'campaign_id', nullable: false, comment: 'ID chiến dịch' })
  campaignId: number;

  @Column({ name: 'sent_count', type: 'int', default: 0, comment: 'Số email đã gửi' })
  sentCount: number;

  @Column({ name: 'delivered_count', type: 'int', default: 0, comment: 'Số email đã giao' })
  deliveredCount: number;

  @Column({ name: 'open_count', type: 'int', default: 0, comment: 'Số email đã mở' })
  openCount: number;

  @Column({ name: 'click_count', type: 'int', default: 0, comment: 'Số click' })
  clickCount: number;

  @Column({ name: 'bounce_count', type: 'int', default: 0, comment: 'Số email bị bounce' })
  bounceCount: number;

  @Column({ name: 'unsubscribe_count', type: 'int', default: 0, comment: 'Số hủy đăng ký' })
  unsubscribeCount: number;

  @Column({ name: 'created_at', type: 'bigint', nullable: false, comment: 'Thời gian tạo' })
  createdAt: number;

  @Column({ name: 'updated_at', type: 'bigint', nullable: true, comment: 'Thời gian cập nhật' })
  updatedAt: number;
}
```

#### 1.1.2. UserEmailCampaignLog Entity
```typescript
// src/modules/marketing/user/entities/user-email-campaign-log.entity.ts
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('user_email_campaign_logs')
export class UserEmailCampaignLog {
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  @Column({ name: 'user_id', nullable: false, comment: 'ID người dùng' })
  userId: number;

  @Column({ name: 'campaign_id', nullable: false, comment: 'ID chiến dịch' })
  campaignId: number;

  @Column({ name: 'audience_id', nullable: false, comment: 'ID đối tượng nhận' })
  audienceId: number;

  @Column({ name: 'email', length: 255, nullable: false, comment: 'Email nhận' })
  email: string;

  @Column({ name: 'status', length: 50, nullable: false, comment: 'Trạng thái gửi' })
  status: string; // 'sent', 'delivered', 'opened', 'clicked', 'bounced', 'unsubscribed'

  @Column({ name: 'event_data', type: 'jsonb', nullable: true, comment: 'Dữ liệu sự kiện' })
  eventData: any;

  @Column({ name: 'occurred_at', type: 'bigint', nullable: false, comment: 'Thời gian xảy ra' })
  occurredAt: number;

  @Column({ name: 'created_at', type: 'bigint', nullable: false, comment: 'Thời gian tạo' })
  createdAt: number;
}
```

### 1.2. Tạo Entities cho SMS Marketing

#### 1.2.1. UserSmsCampaignStats Entity
```typescript
// src/modules/marketing/user/entities/user-sms-campaign-stats.entity.ts
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('user_sms_campaign_stats')
export class UserSmsCampaignStats {
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  @Column({ name: 'user_id', nullable: false, comment: 'ID người dùng' })
  userId: number;

  @Column({ name: 'campaign_id', nullable: false, comment: 'ID chiến dịch' })
  campaignId: number;

  @Column({ name: 'sent_count', type: 'int', default: 0, comment: 'Số SMS đã gửi' })
  sentCount: number;

  @Column({ name: 'delivered_count', type: 'int', default: 0, comment: 'Số SMS đã giao' })
  deliveredCount: number;

  @Column({ name: 'reply_count', type: 'int', default: 0, comment: 'Số phản hồi' })
  replyCount: number;

  @Column({ name: 'failed_count', type: 'int', default: 0, comment: 'Số gửi thất bại' })
  failedCount: number;

  @Column({ name: 'created_at', type: 'bigint', nullable: false, comment: 'Thời gian tạo' })
  createdAt: number;

  @Column({ name: 'updated_at', type: 'bigint', nullable: true, comment: 'Thời gian cập nhật' })
  updatedAt: number;
}
```

#### 1.2.2. UserSmsReply Entity
```typescript
// src/modules/marketing/user/entities/user-sms-reply.entity.ts
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('user_sms_replies')
export class UserSmsReply {
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  @Column({ name: 'user_id', nullable: false, comment: 'ID người dùng' })
  userId: number;

  @Column({ name: 'campaign_id', nullable: true, comment: 'ID chiến dịch (nếu có)' })
  campaignId: number;

  @Column({ name: 'audience_id', nullable: false, comment: 'ID đối tượng phản hồi' })
  audienceId: number;

  @Column({ name: 'phone', length: 20, nullable: false, comment: 'Số điện thoại' })
  phone: string;

  @Column({ name: 'message', type: 'text', nullable: false, comment: 'Nội dung phản hồi' })
  message: string;

  @Column({ name: 'keywords', type: 'jsonb', nullable: true, comment: 'Từ khóa được phát hiện' })
  keywords: string[];

  @Column({ name: 'auto_replied', type: 'boolean', default: false, comment: 'Đã tự động phản hồi' })
  autoReplied: boolean;

  @Column({ name: 'received_at', type: 'bigint', nullable: false, comment: 'Thời gian nhận' })
  receivedAt: number;

  @Column({ name: 'created_at', type: 'bigint', nullable: false, comment: 'Thời gian tạo' })
  createdAt: number;
}
```

### 1.3. Tạo Entity cho A/B Testing

#### 1.3.1. UserCampaignVariant Entity
```typescript
// src/modules/marketing/user/entities/user-campaign-variant.entity.ts
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('user_campaign_variants')
export class UserCampaignVariant {
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  @Column({ name: 'user_id', nullable: false, comment: 'ID người dùng' })
  userId: number;

  @Column({ name: 'campaign_id', nullable: false, comment: 'ID chiến dịch chính' })
  campaignId: number;

  @Column({ name: 'variant_name', length: 100, nullable: false, comment: 'Tên phiên bản' })
  variantName: string; // 'A', 'B', 'C'

  @Column({ name: 'subject', length: 255, nullable: true, comment: 'Tiêu đề (cho email)' })
  subject: string;

  @Column({ name: 'content', type: 'text', nullable: false, comment: 'Nội dung' })
  content: string;

  @Column({ name: 'test_percentage', type: 'int', default: 50, comment: 'Phần trăm test' })
  testPercentage: number;

  @Column({ name: 'is_winner', type: 'boolean', default: false, comment: 'Phiên bản chiến thắng' })
  isWinner: boolean;

  @Column({ name: 'performance_score', type: 'decimal', precision: 5, scale: 2, nullable: true, comment: 'Điểm hiệu suất' })
  performanceScore: number;

  @Column({ name: 'created_at', type: 'bigint', nullable: false, comment: 'Thời gian tạo' })
  createdAt: number;

  @Column({ name: 'updated_at', type: 'bigint', nullable: true, comment: 'Thời gian cập nhật' })
  updatedAt: number;
}
```

## 🎯 Phase 2: DTOs và Validation (Tuần 2-3)

### 2.1. Email Campaign DTOs

#### 2.1.1. Create Email Campaign DTO
```typescript
// src/modules/marketing/user/dto/create-email-campaign.dto.ts
import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsNumber, IsArray, IsEmail, IsObject, ValidateNested, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';

export class EmailServerConfigDto {
  @ApiProperty({ description: 'Loại server email', enum: ['smtp', 'sendgrid', 'ses'] })
  @IsEnum(['smtp', 'sendgrid', 'ses'])
  type: 'smtp' | 'sendgrid' | 'ses';

  @ApiProperty({ description: 'Host SMTP', required: false })
  @IsOptional()
  @IsString()
  host?: string;

  @ApiProperty({ description: 'Port SMTP', required: false })
  @IsOptional()
  @IsNumber()
  port?: number;

  @ApiProperty({ description: 'Username', required: false })
  @IsOptional()
  @IsString()
  username?: string;

  @ApiProperty({ description: 'Password', required: false })
  @IsOptional()
  @IsString()
  password?: string;

  @ApiProperty({ description: 'API Key', required: false })
  @IsOptional()
  @IsString()
  apiKey?: string;

  @ApiProperty({ description: 'Email người gửi' })
  @IsEmail()
  fromEmail: string;

  @ApiProperty({ description: 'Tên người gửi' })
  @IsString()
  @IsNotEmpty()
  fromName: string;

  @ApiProperty({ description: 'Email reply-to', required: false })
  @IsOptional()
  @IsEmail()
  replyTo?: string;
}

export class CreateEmailCampaignDto {
  @ApiProperty({ description: 'Tên chiến dịch' })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({ description: 'Mô tả chiến dịch', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Tiêu đề email' })
  @IsString()
  @IsNotEmpty()
  subject: string;

  @ApiProperty({ description: 'Nội dung email' })
  @IsString()
  @IsNotEmpty()
  content: string;

  @ApiProperty({ description: 'ID template email', required: false })
  @IsOptional()
  @IsNumber()
  templateId?: number;

  @ApiProperty({ description: 'ID segment', required: false })
  @IsOptional()
  @IsNumber()
  segmentId?: number;

  @ApiProperty({ description: 'Danh sách ID audience', required: false })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  audienceIds?: number[];

  @ApiProperty({ description: 'Cấu hình server email', type: EmailServerConfigDto })
  @IsObject()
  @ValidateNested()
  @Type(() => EmailServerConfigDto)
  server: EmailServerConfigDto;

  @ApiProperty({ description: 'Thời gian lên lịch gửi', required: false })
  @IsOptional()
  @IsNumber()
  scheduledAt?: number;

  @ApiProperty({ description: 'Có phải A/B test không', required: false })
  @IsOptional()
  isAbTest?: boolean;
}
```

### 2.2. SMS Campaign DTOs

#### 2.2.1. Create SMS Campaign DTO
```typescript
// src/modules/marketing/user/dto/create-sms-campaign.dto.ts
import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsNumber, IsArray, IsObject, ValidateNested, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';

export class SmsServerConfigDto {
  @ApiProperty({ description: 'Loại nhà cung cấp SMS', enum: ['twilio', 'nexmo', 'infobip'] })
  @IsEnum(['twilio', 'nexmo', 'infobip'])
  type: 'twilio' | 'nexmo' | 'infobip';

  @ApiProperty({ description: 'API Key' })
  @IsString()
  @IsNotEmpty()
  apiKey: string;

  @ApiProperty({ description: 'API Secret', required: false })
  @IsOptional()
  @IsString()
  apiSecret?: string;

  @ApiProperty({ description: 'Sender ID' })
  @IsString()
  @IsNotEmpty()
  senderId: string;

  @ApiProperty({ description: 'Account SID (Twilio)', required: false })
  @IsOptional()
  @IsString()
  accountSid?: string;
}

export class CreateSmsCampaignDto {
  @ApiProperty({ description: 'Tên chiến dịch' })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({ description: 'Mô tả chiến dịch', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Nội dung SMS' })
  @IsString()
  @IsNotEmpty()
  content: string;

  @ApiProperty({ description: 'ID template SMS', required: false })
  @IsOptional()
  @IsNumber()
  templateId?: number;

  @ApiProperty({ description: 'ID segment', required: false })
  @IsOptional()
  @IsNumber()
  segmentId?: number;

  @ApiProperty({ description: 'Danh sách ID audience', required: false })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  audienceIds?: number[];

  @ApiProperty({ description: 'Cấu hình server SMS', type: SmsServerConfigDto })
  @IsObject()
  @ValidateNested()
  @Type(() => SmsServerConfigDto)
  server: SmsServerConfigDto;

  @ApiProperty({ description: 'Thời gian lên lịch gửi', required: false })
  @IsOptional()
  @IsNumber()
  scheduledAt?: number;
}
```

## 🎯 Phase 3: Repositories (Tuần 3-4)

### 3.1. Email Campaign Stats Repository

```typescript
// src/modules/marketing/user/repositories/user-email-campaign-stats.repository.ts
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindManyOptions, FindOneOptions } from 'typeorm';
import { UserEmailCampaignStats } from '../entities/user-email-campaign-stats.entity';

@Injectable()
export class UserEmailCampaignStatsRepository {
  constructor(
    @InjectRepository(UserEmailCampaignStats)
    private readonly repository: Repository<UserEmailCampaignStats>,
  ) {}

  async find(options?: FindManyOptions<UserEmailCampaignStats>): Promise<UserEmailCampaignStats[]> {
    return this.repository.find(options);
  }

  async findOne(options?: FindOneOptions<UserEmailCampaignStats>): Promise<UserEmailCampaignStats | null> {
    return this.repository.findOne(options);
  }

  async findByCampaignId(campaignId: number, userId: number): Promise<UserEmailCampaignStats | null> {
    return this.repository.findOne({
      where: { campaignId, userId }
    });
  }

  async save(entity: UserEmailCampaignStats): Promise<UserEmailCampaignStats> {
    return this.repository.save(entity);
  }

  async incrementSentCount(campaignId: number, userId: number, count: number = 1): Promise<void> {
    await this.repository.increment(
      { campaignId, userId },
      'sentCount',
      count
    );
  }

  async incrementDeliveredCount(campaignId: number, userId: number, count: number = 1): Promise<void> {
    await this.repository.increment(
      { campaignId, userId },
      'deliveredCount',
      count
    );
  }

  async incrementOpenCount(campaignId: number, userId: number, count: number = 1): Promise<void> {
    await this.repository.increment(
      { campaignId, userId },
      'openCount',
      count
    );
  }

  async incrementClickCount(campaignId: number, userId: number, count: number = 1): Promise<void> {
    await this.repository.increment(
      { campaignId, userId },
      'clickCount',
      count
    );
  }

  async incrementBounceCount(campaignId: number, userId: number, count: number = 1): Promise<void> {
    await this.repository.increment(
      { campaignId, userId },
      'bounceCount',
      count
    );
  }

  async incrementUnsubscribeCount(campaignId: number, userId: number, count: number = 1): Promise<void> {
    await this.repository.increment(
      { campaignId, userId },
      'unsubscribeCount',
      count
    );
  }
}
```

## 🎯 Phase 4: Services Implementation (Tuần 4-6)

### 4.1. Email Campaign Service

```typescript
// src/modules/marketing/user/services/user-email-campaign.service.ts
import { Injectable } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import { AppException } from '@/common/exceptions/app.exception';
import { ErrorCode } from '@/common/constants/error-code.constant';
import { UserCampaignRepository } from '../repositories/user-campaign.repository';
import { UserEmailCampaignStatsRepository } from '../repositories/user-email-campaign-stats.repository';
import { CreateEmailCampaignDto } from '../dto/create-email-campaign.dto';
import { UserCampaign } from '../entities/user-campaign.entity';
import { UserEmailCampaignStats } from '../entities/user-email-campaign-stats.entity';

@Injectable()
export class UserEmailCampaignService {
  constructor(
    private readonly userCampaignRepository: UserCampaignRepository,
    private readonly userEmailCampaignStatsRepository: UserEmailCampaignStatsRepository,
  ) {}

  @Transactional()
  async createEmailCampaign(
    createDto: CreateEmailCampaignDto,
    userId: number
  ): Promise<UserCampaign> {
    const now = Date.now();

    // Tạo campaign với platform = 'email'
    const campaign = new UserCampaign();
    campaign.userId = userId;
    campaign.title = createDto.title;
    campaign.description = createDto.description;
    campaign.platform = 'email';
    campaign.content = createDto.content;
    campaign.subject = createDto.subject;
    campaign.server = createDto.server;
    campaign.segmentId = createDto.segmentId;
    campaign.audienceIds = createDto.audienceIds;
    campaign.scheduledAt = createDto.scheduledAt;
    campaign.status = 'draft';
    campaign.createdAt = now;
    campaign.updatedAt = now;

    const savedCampaign = await this.userCampaignRepository.save(campaign);

    // Tạo stats record
    const stats = new UserEmailCampaignStats();
    stats.userId = userId;
    stats.campaignId = savedCampaign.id;
    stats.createdAt = now;
    stats.updatedAt = now;

    await this.userEmailCampaignStatsRepository.save(stats);

    return savedCampaign;
  }

  async findEmailCampaigns(userId: number): Promise<UserCampaign[]> {
    return this.userCampaignRepository.find({
      where: { userId, platform: 'email' },
      order: { createdAt: 'DESC' }
    });
  }

  async findEmailCampaignById(id: number, userId: number): Promise<UserCampaign> {
    const campaign = await this.userCampaignRepository.findOne({
      where: { id, userId, platform: 'email' }
    });

    if (!campaign) {
      throw new AppException(ErrorCode.CAMPAIGN_NOT_FOUND);
    }

    return campaign;
  }

  async getEmailCampaignStats(campaignId: number, userId: number): Promise<UserEmailCampaignStats> {
    const stats = await this.userEmailCampaignStatsRepository.findByCampaignId(campaignId, userId);
    
    if (!stats) {
      throw new AppException(ErrorCode.CAMPAIGN_STATS_NOT_FOUND);
    }

    return stats;
  }

  @Transactional()
  async sendEmailCampaign(campaignId: number, userId: number): Promise<boolean> {
    const campaign = await this.findEmailCampaignById(campaignId, userId);

    if (campaign.status !== 'draft' && campaign.status !== 'scheduled') {
      throw new AppException(ErrorCode.CAMPAIGN_CANNOT_BE_SENT);
    }

    // Update campaign status
    campaign.status = 'sending';
    campaign.updatedAt = Date.now();
    await this.userCampaignRepository.save(campaign);

    // TODO: Implement actual email sending logic
    // This will be implemented in Phase 5

    return true;
  }
}
```

## 🎯 Phase 5: Email/SMS Sending Services (Tuần 6-8)

### 5.1. Email Sending Service

```typescript
// src/modules/marketing/user/services/email-sending.service.ts
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';
import * as sgMail from '@sendgrid/mail';
import { SESClient, SendEmailCommand } from '@aws-sdk/client-ses';
import { UserEmailCampaignLog } from '../entities/user-email-campaign-log.entity';
import { UserEmailCampaignLogRepository } from '../repositories/user-email-campaign-log.repository';
import { UserEmailCampaignStatsRepository } from '../repositories/user-email-campaign-stats.repository';

interface EmailServerConfig {
  type: 'smtp' | 'sendgrid' | 'ses';
  host?: string;
  port?: number;
  username?: string;
  password?: string;
  apiKey?: string;
  fromEmail: string;
  fromName: string;
  replyTo?: string;
}

@Injectable()
export class EmailSendingService {
  constructor(
    private readonly configService: ConfigService,
    private readonly emailCampaignLogRepository: UserEmailCampaignLogRepository,
    private readonly emailCampaignStatsRepository: UserEmailCampaignStatsRepository,
  ) {}

  async sendEmail(
    campaignId: number,
    userId: number,
    audienceId: number,
    email: string,
    subject: string,
    content: string,
    serverConfig: EmailServerConfig
  ): Promise<boolean> {
    try {
      let result: boolean = false;

      switch (serverConfig.type) {
        case 'smtp':
          result = await this.sendViaSMTP(email, subject, content, serverConfig);
          break;
        case 'sendgrid':
          result = await this.sendViaSendGrid(email, subject, content, serverConfig);
          break;
        case 'ses':
          result = await this.sendViaSES(email, subject, content, serverConfig);
          break;
      }

      // Log email sending
      await this.logEmailEvent(campaignId, userId, audienceId, email, 'sent', {
        serverType: serverConfig.type,
        timestamp: Date.now()
      });

      // Update stats
      await this.emailCampaignStatsRepository.incrementSentCount(campaignId, userId);

      return result;
    } catch (error) {
      // Log failed email
      await this.logEmailEvent(campaignId, userId, audienceId, email, 'failed', {
        error: error.message,
        timestamp: Date.now()
      });

      return false;
    }
  }

  private async sendViaSMTP(
    email: string,
    subject: string,
    content: string,
    config: EmailServerConfig
  ): Promise<boolean> {
    const transporter = nodemailer.createTransporter({
      host: config.host,
      port: config.port,
      secure: config.port === 465,
      auth: {
        user: config.username,
        pass: config.password,
      },
    });

    const mailOptions = {
      from: `${config.fromName} <${config.fromEmail}>`,
      to: email,
      subject: subject,
      html: content,
      replyTo: config.replyTo,
    };

    const result = await transporter.sendMail(mailOptions);
    return !!result.messageId;
  }

  private async sendViaSendGrid(
    email: string,
    subject: string,
    content: string,
    config: EmailServerConfig
  ): Promise<boolean> {
    sgMail.setApiKey(config.apiKey);

    const msg = {
      to: email,
      from: {
        email: config.fromEmail,
        name: config.fromName,
      },
      subject: subject,
      html: content,
      replyTo: config.replyTo,
    };

    const result = await sgMail.send(msg);
    return result[0].statusCode >= 200 && result[0].statusCode < 300;
  }

  private async sendViaSES(
    email: string,
    subject: string,
    content: string,
    config: EmailServerConfig
  ): Promise<boolean> {
    const sesClient = new SESClient({
      region: this.configService.get('AWS_REGION'),
      credentials: {
        accessKeyId: this.configService.get('AWS_ACCESS_KEY_ID'),
        secretAccessKey: this.configService.get('AWS_SECRET_ACCESS_KEY'),
      },
    });

    const command = new SendEmailCommand({
      Source: `${config.fromName} <${config.fromEmail}>`,
      Destination: {
        ToAddresses: [email],
      },
      Message: {
        Subject: {
          Data: subject,
          Charset: 'UTF-8',
        },
        Body: {
          Html: {
            Data: content,
            Charset: 'UTF-8',
          },
        },
      },
      ReplyToAddresses: config.replyTo ? [config.replyTo] : undefined,
    });

    const result = await sesClient.send(command);
    return !!result.MessageId;
  }

  private async logEmailEvent(
    campaignId: number,
    userId: number,
    audienceId: number,
    email: string,
    status: string,
    eventData: any
  ): Promise<void> {
    const log = new UserEmailCampaignLog();
    log.userId = userId;
    log.campaignId = campaignId;
    log.audienceId = audienceId;
    log.email = email;
    log.status = status;
    log.eventData = eventData;
    log.occurredAt = Date.now();
    log.createdAt = Date.now();

    await this.emailCampaignLogRepository.save(log);
  }
}
```

### 5.2. SMS Sending Service

```typescript
// src/modules/marketing/user/services/sms-sending.service.ts
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Twilio } from 'twilio';
import axios from 'axios';
import { UserSmsCampaignStats } from '../entities/user-sms-campaign-stats.entity';
import { UserSmsCampaignStatsRepository } from '../repositories/user-sms-campaign-stats.repository';

interface SmsServerConfig {
  type: 'twilio' | 'nexmo' | 'infobip';
  apiKey: string;
  apiSecret?: string;
  senderId: string;
  accountSid?: string;
}

@Injectable()
export class SmsSendingService {
  constructor(
    private readonly configService: ConfigService,
    private readonly smsCampaignStatsRepository: UserSmsCampaignStatsRepository,
  ) {}

  async sendSms(
    campaignId: number,
    userId: number,
    audienceId: number,
    phone: string,
    content: string,
    serverConfig: SmsServerConfig
  ): Promise<boolean> {
    try {
      let result: boolean = false;

      switch (serverConfig.type) {
        case 'twilio':
          result = await this.sendViaTwilio(phone, content, serverConfig);
          break;
        case 'nexmo':
          result = await this.sendViaNexmo(phone, content, serverConfig);
          break;
        case 'infobip':
          result = await this.sendViaInfobip(phone, content, serverConfig);
          break;
      }

      // Update stats
      if (result) {
        await this.smsCampaignStatsRepository.incrementSentCount(campaignId, userId);
      } else {
        await this.smsCampaignStatsRepository.incrementFailedCount(campaignId, userId);
      }

      return result;
    } catch (error) {
      await this.smsCampaignStatsRepository.incrementFailedCount(campaignId, userId);
      return false;
    }
  }

  private async sendViaTwilio(
    phone: string,
    content: string,
    config: SmsServerConfig
  ): Promise<boolean> {
    const client = new Twilio(config.accountSid, config.apiKey);

    const message = await client.messages.create({
      body: content,
      from: config.senderId,
      to: phone,
    });

    return !!message.sid;
  }

  private async sendViaNexmo(
    phone: string,
    content: string,
    config: SmsServerConfig
  ): Promise<boolean> {
    const response = await axios.post('https://rest.nexmo.com/sms/json', {
      api_key: config.apiKey,
      api_secret: config.apiSecret,
      from: config.senderId,
      to: phone,
      text: content,
    });

    return response.data.messages[0].status === '0';
  }

  private async sendViaInfobip(
    phone: string,
    content: string,
    config: SmsServerConfig
  ): Promise<boolean> {
    const response = await axios.post(
      'https://api.infobip.com/sms/2/text/advanced',
      {
        messages: [
          {
            from: config.senderId,
            destinations: [{ to: phone }],
            text: content,
          },
        ],
      },
      {
        headers: {
          Authorization: `App ${config.apiKey}`,
          'Content-Type': 'application/json',
        },
      }
    );

    return response.data.messages[0].status.groupId === 1;
  }
}
```
